# Istruzioni per Testare la Soluzione Anti-Duplicati

## Problema Risolto
Il microservizio ora previene la creazione di record duplicati quando viene effettuata più volte la POST create della survey per lo stesso `orderCode`.

## Come Testare

### 1. Test Automatici

#### Test del Repository
```bash
mvnw test -Dtest=AnswerRepositoryTest
```

#### Test del Servizio (Semplificato)
```bash
mvnw test -Dtest=AnswerServiceSimpleTest
```

#### Test del Servizio (Completo - potrebbe richiedere mock)
```bash
mvnw test -Dtest=AnswerServiceDuplicateTest
```

### 2. Test Manuali con API

#### Scenario 1: Prima Creazione
```bash
POST /answers
Content-Type: application/json
Authorization: Bearer <token>

{
  "data": [
    {
      "orderCode": "ORDER123",
      "productId": 1,
      "questionId": 10,
      "answerId": 100,
      "customerId": 1000,
      "orderItemId": 10000
    },
    {
      "orderCode": "ORDER123",
      "productId": 1,
      "questionId": 20,
      "answerId": 200,
      "customerId": 1000,
      "orderItemId": 10000
    }
  ]
}
```

**Risultato Atteso**: 2 record creati nel database

#### Scenario 2: Seconda Creazione (Stesso OrderCode)
```bash
POST /answers
Content-Type: application/json
Authorization: Bearer <token>

{
  "data": [
    {
      "orderCode": "ORDER123",
      "productId": 1,
      "questionId": 10,
      "answerId": 300,
      "customerId": 1000,
      "orderItemId": 10000
    },
    {
      "orderCode": "ORDER123",
      "productId": 1,
      "questionId": 20,
      "answerId": 400,
      "customerId": 1000,
      "orderItemId": 10000
    }
  ]
}
```

**Risultato Atteso**: 
- Ancora 2 record nel database (NON 4)
- I record precedenti sono stati sostituiti
- Le nuove risposte hanno answerId 300 e 400

#### Scenario 3: Verifica Risposte Esistenti
```bash
GET /answers/order/ORDER123
Authorization: Bearer <token>
```

**Risultato Atteso**: 
- 2 risposte con answerId 300 e 400
- NON ci sono più le risposte con answerId 100 e 200

### 3. Verifica Database

#### Query per Verificare i Dati
```sql
-- Conta tutte le risposte per un orderCode
SELECT COUNT(*) FROM survey.answers WHERE order_code = 'ORDER123';

-- Mostra tutte le risposte per un orderCode
SELECT * FROM survey.answers WHERE order_code = 'ORDER123' ORDER BY created_at;

-- Verifica che non ci siano duplicati
SELECT order_code, survey_question_id, survey_answers_id, COUNT(*) 
FROM survey.answers 
WHERE order_code = 'ORDER123'
GROUP BY order_code, survey_question_id, survey_answers_id
HAVING COUNT(*) > 1;
```

## Comportamento Prima vs Dopo

### Prima del Fix
```
POST 1: ORDER123 → 2 record creati
POST 2: ORDER123 → 4 record totali (DUPLICATI!)
POST 3: ORDER123 → 6 record totali (PIÙ DUPLICATI!)
```

### Dopo il Fix
```
POST 1: ORDER123 → 2 record creati
POST 2: ORDER123 → 2 record totali (sostituiti)
POST 3: ORDER123 → 2 record totali (sostituiti di nuovo)
```

## Configurazione Test

### File di Configurazione Test
Il file `src/test/resources/application.properties` è configurato per:
- Usare database H2 in memoria
- Disabilitare sicurezza e multitenant per i test
- Creare automaticamente gli schemi necessari

### Dipendenze Richieste
Assicurati che il `pom.xml` includa:
- `quarkus-test-h2` per i test con H2
- `quarkus-junit5` per i test JUnit 5
- `quarkus-test-transaction` per `@TestTransaction`

## Troubleshooting

### Errore "Client non disponibile"
Se i test del servizio falliscono per client non disponibili:
1. Usa `AnswerServiceSimpleTest` che non ha dipendenze esterne
2. Oppure mocka i client con `@InjectMock`

### Errore "Schema non trovato"
Verifica che la configurazione test includa:
```properties
quarkus.hibernate-orm.database.generation.create-schemas=true
```

### Errore "Multitenant"
Assicurati che nei test sia disabilitato:
```properties
quarkus.hibernate-orm.multitenant=NONE
```

## Deployment in Produzione

1. **Backup Database**: Prima di deployare, fai backup del database
2. **Test in Staging**: Testa la soluzione in ambiente di staging
3. **Constraint Opzionale**: Considera l'applicazione dello script SQL per il constraint unico
4. **Monitoraggio**: Monitora i log per verificare che non ci siano più duplicati

## Script SQL Opzionale

Per maggiore sicurezza, puoi applicare il constraint unico:
```sql
-- Rimuovi duplicati esistenti
DELETE FROM survey.answers a1 
USING survey.answers a2 
WHERE a1.id < a2.id 
  AND a1.order_code = a2.order_code 
  AND a1.survey_question_id = a2.survey_question_id 
  AND a1.survey_answers_id = a2.survey_answers_id;

-- Aggiungi constraint unico
ALTER TABLE survey.answers 
ADD CONSTRAINT uk_answers_order_question_answer 
UNIQUE (order_code, survey_question_id, survey_answers_id);
```
