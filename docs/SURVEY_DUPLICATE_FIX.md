# Fix per Duplicati Survey

## Problema
Il microservizio permetteva la creazione di record duplicati quando veniva effettuata più volte la POST create della survey per lo stesso `orderCode` con le stesse domande e risposte.

## Soluzione Implementata

### 1. Appro<PERSON>o "Upsert" (Update or Insert)
La soluzione implementa un pattern **upsert** che:
- Verifica se esistono già risposte per l'`orderCode`
- Se esistono, le elimina prima di inserire quelle nuove
- Se non esistono, inserisce direttamente le nuove risposte

### 2. Modifiche al Codice

#### AnswerRepository.java
Aggiunti metodi per:
- `findByOrderCode(String orderCode)` - Trova risposte per orderCode
- `existsByOrderCode(String orderCode)` - Verifica esistenza risposte
- `deleteByOrderCode(String orderCode)` - Elimina risposte per orderCode
- `findByOrderCodeAndQuestionAndAnswer()` - Ricerca specifica

#### AnswerService.java
- Modificato `createAnswers()` per chiamare `createOrUpdateAnswers()`
- Aggiunto `createOrUpdateAnswers()` con logica upsert
- Aggiunti metodi di utilità per gestire le risposte esistenti

#### AnswerResource.java
- Aggiunto endpoint `GET /order/{orderCode}` per recuperare risposte esistenti

### 3. Constraint Database (Opzionale)
Creato script SQL per aggiungere constraint unico:
```sql
ALTER TABLE survey.answers 
ADD CONSTRAINT uk_answers_order_question_answer 
UNIQUE (order_code, survey_question_id, survey_answers_id);
```

## Comportamento Attuale

### Prima del Fix
```
POST /answers (orderCode: ABC123, questions: [1,2], answers: [A,B])
→ Crea 2 record

POST /answers (orderCode: ABC123, questions: [1,2], answers: [A,B])  
→ Crea altri 2 record (DUPLICATI!)
```

### Dopo il Fix
```
POST /answers (orderCode: ABC123, questions: [1,2], answers: [A,B])
→ Crea 2 record

POST /answers (orderCode: ABC123, questions: [1,2], answers: [A,B])
→ Sostituisce i 2 record esistenti (NO DUPLICATI)
```

## API Endpoints

### Esistenti (modificati)
- `POST /answers` - Ora implementa logica upsert

### Nuovi
- `GET /answers/order/{orderCode}` - Recupera risposte per orderCode

## Test
Creato `AnswerServiceDuplicateTest.java` che verifica:
- Creazione iniziale funziona correttamente
- Chiamate multiple non creano duplicati
- Le risposte vengono sostituite correttamente

## Esecuzione Test
```bash
./mvnw test -Dtest=AnswerServiceDuplicateTest
```

## Note Tecniche

### Transazionalità
- Operazioni di delete + insert sono atomiche grazie a `@Transactional`
- Uso di `flush()` per garantire che la cancellazione sia completata prima dell'inserimento

### Performance
- La soluzione è efficiente per volumi normali di survey
- Per volumi molto elevati, considerare ottimizzazioni specifiche

### Backward Compatibility
- La soluzione è completamente backward compatible
- Non richiede modifiche ai client esistenti
- L'API mantiene la stessa interfaccia

## Deployment
1. Applicare le modifiche al codice
2. Opzionalmente eseguire lo script SQL per il constraint
3. Eseguire i test per verificare il funzionamento
4. Deployare in ambiente di test prima della produzione
