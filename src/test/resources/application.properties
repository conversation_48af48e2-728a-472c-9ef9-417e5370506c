quarkus.datasource.db-kind=h2
quarkus.datasource.jdbc.url=jdbc:h2:mem:test;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE;MODE=PostgreSQL;INIT=CREATE SCHEMA IF NOT EXISTS SURVEY\\;CREATE SCHEMA IF NOT EXISTS PRODUCT;
quarkus.datasource.username=sa
quarkus.datasource.password=sa
quarkus.hibernate-orm.database.generation=drop-and-create
quarkus.hibernate-orm.database.generation.create-schemas=true
quarkus.hibernate-orm.multitenant=NONE
quarkus.datasource.jdbc.driver=org.h2.Driver

# Disabilita sicurezza per i test
quarkus.http.auth.proactive=false
quarkus.oauth2.enabled=false
quarkus.security.enabled=false
quarkus.smallrye-jwt.enabled=false

# Disabilita OpenTelemetry per i test
quarkus.opentelemetry.enabled=false
quarkus.opentelemetry.tracer.enabled=false

# Logging per debug
quarkus.log.level=INFO
quarkus.log.category."it.yolo".level=DEBUG
