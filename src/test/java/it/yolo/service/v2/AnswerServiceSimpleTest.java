package it.yolo.service.v2;

import io.quarkus.test.junit.QuarkusTest;
import io.quarkus.test.TestTransaction;
import it.yolo.entity.AnswerEntity;
import it.yolo.repository.AnswerRepository;
import org.junit.jupiter.api.Test;

import javax.inject.Inject;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test semplificato che testa solo la logica di base del repository
 * senza dipendenze esterne che potrebbero causare problemi nei test
 */
@QuarkusTest
public class AnswerServiceSimpleTest {

    @Inject
    AnswerRepository answerRepository;

    private static final String TEST_ORDER_CODE = "TEST_ORDER_SIMPLE";

    @Test
    @TestTransaction
    void testUpsertLogic_CreateThenReplace() {
        // Test della logica upsert manuale
        
        // 1. Verifica che non esistano risposte inizialmente
        assertFalse(answerRepository.existsByOrderCode(TEST_ORDER_CODE));
        assertEquals(0, answerRepository.findByOrderCode(TEST_ORDER_CODE).size());

        // 2. Crea le prime risposte
        List<AnswerEntity> firstAnswers = createTestAnswers(100L, 200L);
        answerRepository.persist(firstAnswers);
        
        // Verifica che siano state create
        assertTrue(answerRepository.existsByOrderCode(TEST_ORDER_CODE));
        assertEquals(2, answerRepository.findByOrderCode(TEST_ORDER_CODE).size());

        // 3. Simula la logica upsert: elimina e ricrea
        answerRepository.deleteByOrderCode(TEST_ORDER_CODE);
        answerRepository.flush(); // Assicura che la cancellazione sia completata
        
        List<AnswerEntity> secondAnswers = createTestAnswers(300L, 400L);
        answerRepository.persist(secondAnswers);

        // 4. Verifica che ci siano ancora solo 2 risposte (non 4)
        List<AnswerEntity> finalAnswers = answerRepository.findByOrderCode(TEST_ORDER_CODE);
        assertEquals(2, finalAnswers.size());
        
        // 5. Verifica che le risposte siano quelle nuove
        assertTrue(finalAnswers.stream().anyMatch(a -> a.getAnswerId().equals(300L)));
        assertTrue(finalAnswers.stream().anyMatch(a -> a.getAnswerId().equals(400L)));
        
        // 6. Verifica che le vecchie risposte non ci siano più
        assertFalse(finalAnswers.stream().anyMatch(a -> a.getAnswerId().equals(100L)));
        assertFalse(finalAnswers.stream().anyMatch(a -> a.getAnswerId().equals(200L)));
    }

    @Test
    @TestTransaction
    void testRepositoryMethods() {
        // Test dei metodi del repository
        
        // 1. Test findByOrderCode con orderCode inesistente
        List<AnswerEntity> empty = answerRepository.findByOrderCode("NON_EXISTING");
        assertTrue(empty.isEmpty());

        // 2. Test existsByOrderCode con orderCode inesistente
        assertFalse(answerRepository.existsByOrderCode("NON_EXISTING"));

        // 3. Crea alcune risposte
        List<AnswerEntity> answers = createTestAnswers(100L, 200L);
        answerRepository.persist(answers);

        // 4. Test findByOrderCode con orderCode esistente
        List<AnswerEntity> found = answerRepository.findByOrderCode(TEST_ORDER_CODE);
        assertEquals(2, found.size());

        // 5. Test existsByOrderCode con orderCode esistente
        assertTrue(answerRepository.existsByOrderCode(TEST_ORDER_CODE));

        // 6. Test deleteByOrderCode
        long deletedCount = answerRepository.deleteByOrderCode(TEST_ORDER_CODE);
        assertEquals(2, deletedCount);
        
        // 7. Verifica che siano state eliminate
        assertFalse(answerRepository.existsByOrderCode(TEST_ORDER_CODE));
        assertTrue(answerRepository.findByOrderCode(TEST_ORDER_CODE).isEmpty());
    }

    private List<AnswerEntity> createTestAnswers(Long answerId1, Long answerId2) {
        AnswerEntity answer1 = new AnswerEntity();
        answer1.setOrderCode(TEST_ORDER_CODE);
        answer1.setProductId(1L);
        answer1.setQuestionId(10L);
        answer1.setAnswerId(answerId1);
        answer1.setCustomerId(1000L);
        answer1.setOrderItemId(10000L);
        answer1.setCreatedBy("test-user");
        answer1.setUpdatedBy("test-user");

        AnswerEntity answer2 = new AnswerEntity();
        answer2.setOrderCode(TEST_ORDER_CODE);
        answer2.setProductId(1L);
        answer2.setQuestionId(20L);
        answer2.setAnswerId(answerId2);
        answer2.setCustomerId(1000L);
        answer2.setOrderItemId(10000L);
        answer2.setCreatedBy("test-user");
        answer2.setUpdatedBy("test-user");

        return Arrays.asList(answer1, answer2);
    }
}
