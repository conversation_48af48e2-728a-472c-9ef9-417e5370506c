package it.yolo.service.v2;

import io.quarkus.test.junit.QuarkusTest;
import it.yolo.entity.AnswerEntity;
import it.yolo.repository.AnswerRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import javax.inject.Inject;
import javax.transaction.Transactional;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@QuarkusTest
public class AnswerServiceDuplicateTest {

    @Inject
    AnswerService answerService;

    @Inject
    AnswerRepository answerRepository;

    private static final String TEST_ORDER_CODE = "TEST_ORDER_001";
    private static final String TEST_TOKEN = "Bearer test-token";

    @BeforeEach
    @Transactional
    void cleanUp() {
        // Pulisci i dati di test prima di ogni test
        answerRepository.deleteByOrderCode(TEST_ORDER_CODE);
    }

    @Test
    @org.junit.jupiter.api.Order(1)
    void testCreateAnswers_FirstTime_ShouldCreateSuccessfully() {
        // Arrange
        List<AnswerEntity> answers = createTestAnswers();

        // Act
        List<AnswerEntity> result = answerService.createAnswers(answers, TEST_TOKEN);

        // Assert
        assertNotNull(result);
        assertEquals(2, result.size());
        
        // Verifica che siano state create nel database
        List<AnswerEntity> dbAnswers = answerService.getAnswersByOrderCode(TEST_ORDER_CODE);
        assertEquals(2, dbAnswers.size());
    }

    @Test
    @org.junit.jupiter.api.Order(2)
    void testCreateAnswers_SecondTime_ShouldReplaceExisting() {
        // Arrange - Prima creazione
        List<AnswerEntity> firstAnswers = createTestAnswers();
        answerService.createAnswers(firstAnswers, TEST_TOKEN);
        
        // Verifica che esistano
        assertTrue(answerService.existsAnswersByOrderCode(TEST_ORDER_CODE));
        assertEquals(2, answerService.getAnswersByOrderCode(TEST_ORDER_CODE).size());

        // Seconda creazione con risposte diverse
        List<AnswerEntity> secondAnswers = createTestAnswersWithDifferentValues();

        // Act
        List<AnswerEntity> result = answerService.createAnswers(secondAnswers, TEST_TOKEN);

        // Assert
        assertNotNull(result);
        assertEquals(2, result.size());
        
        // Verifica che ci siano ancora solo 2 risposte (non 4)
        List<AnswerEntity> dbAnswers = answerService.getAnswersByOrderCode(TEST_ORDER_CODE);
        assertEquals(2, dbAnswers.size());
        
        // Verifica che le risposte siano quelle nuove
        assertTrue(dbAnswers.stream().anyMatch(a -> a.getAnswerId().equals(300L)));
        assertTrue(dbAnswers.stream().anyMatch(a -> a.getAnswerId().equals(400L)));
    }

    @Test
    @org.junit.jupiter.api.Order(3)
    void testExistsAnswersByOrderCode() {
        // Arrange
        assertFalse(answerService.existsAnswersByOrderCode(TEST_ORDER_CODE));
        
        List<AnswerEntity> answers = createTestAnswers();
        answerService.createAnswers(answers, TEST_TOKEN);

        // Act & Assert
        assertTrue(answerService.existsAnswersByOrderCode(TEST_ORDER_CODE));
        assertFalse(answerService.existsAnswersByOrderCode("NON_EXISTING_ORDER"));
    }

    private List<AnswerEntity> createTestAnswers() {
        AnswerEntity answer1 = new AnswerEntity();
        answer1.setOrderCode(TEST_ORDER_CODE);
        answer1.setProductId(1L);
        answer1.setQuestionId(10L);
        answer1.setAnswerId(100L);
        answer1.setCustomerId(1000L);
        answer1.setOrderItemId(10000L);
        answer1.setCreatedBy("test-user");
        answer1.setUpdatedBy("test-user");

        AnswerEntity answer2 = new AnswerEntity();
        answer2.setOrderCode(TEST_ORDER_CODE);
        answer2.setProductId(1L);
        answer2.setQuestionId(20L);
        answer2.setAnswerId(200L);
        answer2.setCustomerId(1000L);
        answer2.setOrderItemId(10000L);
        answer2.setCreatedBy("test-user");
        answer2.setUpdatedBy("test-user");

        return Arrays.asList(answer1, answer2);
    }

    private List<AnswerEntity> createTestAnswersWithDifferentValues() {
        AnswerEntity answer1 = new AnswerEntity();
        answer1.setOrderCode(TEST_ORDER_CODE);
        answer1.setProductId(1L);
        answer1.setQuestionId(10L);
        answer1.setAnswerId(300L); // Risposta diversa
        answer1.setCustomerId(1000L);
        answer1.setOrderItemId(10000L);
        answer1.setCreatedBy("test-user");
        answer1.setUpdatedBy("test-user");

        AnswerEntity answer2 = new AnswerEntity();
        answer2.setOrderCode(TEST_ORDER_CODE);
        answer2.setProductId(1L);
        answer2.setQuestionId(20L);
        answer2.setAnswerId(400L); // Risposta diversa
        answer2.setCustomerId(1000L);
        answer2.setOrderItemId(10000L);
        answer2.setCreatedBy("test-user");
        answer2.setUpdatedBy("test-user");

        return Arrays.asList(answer1, answer2);
    }
}
