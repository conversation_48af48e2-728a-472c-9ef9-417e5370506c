package it.yolo.repository;

import io.quarkus.test.junit.QuarkusTest;
import io.quarkus.test.TestTransaction;
import io.quarkus.test.junit.mockito.InjectMock;
import it.yolo.entity.AnswerEntity;
import org.junit.jupiter.api.Test;

import javax.inject.Inject;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@QuarkusTest
public class AnswerRepositoryTest {

    @Inject
    AnswerRepository answerRepository;

    private static final String TEST_ORDER_CODE = "TEST_ORDER_001";

    @Test
    @TestTransaction
    void testFindByOrderCode() {
        // Arrange
        AnswerEntity answer1 = createTestAnswer(TEST_ORDER_CODE, 10L, 100L);
        AnswerEntity answer2 = createTestAnswer(TEST_ORDER_CODE, 20L, 200L);
        
        answerRepository.persist(answer1);
        answerRepository.persist(answer2);

        // Act
        List<AnswerEntity> results = answerRepository.findByOrderCode(TEST_ORDER_CODE);

        // Assert
        assertEquals(2, results.size());
        assertTrue(results.stream().anyMatch(a -> a.getQuestionId().equals(10L)));
        assertTrue(results.stream().anyMatch(a -> a.getQuestionId().equals(20L)));
    }

    @Test
    @TestTransaction
    void testExistsByOrderCode() {
        // Arrange
        assertFalse(answerRepository.existsByOrderCode(TEST_ORDER_CODE));
        
        AnswerEntity answer = createTestAnswer(TEST_ORDER_CODE, 10L, 100L);
        answerRepository.persist(answer);

        // Act & Assert
        assertTrue(answerRepository.existsByOrderCode(TEST_ORDER_CODE));
        assertFalse(answerRepository.existsByOrderCode("NON_EXISTING_ORDER"));
    }

    @Test
    @TestTransaction
    void testDeleteByOrderCode() {
        // Arrange
        AnswerEntity answer1 = createTestAnswer(TEST_ORDER_CODE, 10L, 100L);
        AnswerEntity answer2 = createTestAnswer(TEST_ORDER_CODE, 20L, 200L);
        AnswerEntity answer3 = createTestAnswer("OTHER_ORDER", 30L, 300L);
        
        answerRepository.persist(answer1);
        answerRepository.persist(answer2);
        answerRepository.persist(answer3);

        // Verifica che ci siano 3 record totali
        assertEquals(3, answerRepository.count());
        assertEquals(2, answerRepository.findByOrderCode(TEST_ORDER_CODE).size());

        // Act
        long deletedCount = answerRepository.deleteByOrderCode(TEST_ORDER_CODE);

        // Assert
        assertEquals(2, deletedCount);
        assertEquals(1, answerRepository.count()); // Rimane solo quello con OTHER_ORDER
        assertEquals(0, answerRepository.findByOrderCode(TEST_ORDER_CODE).size());
        assertEquals(1, answerRepository.findByOrderCode("OTHER_ORDER").size());
    }

    @Test
    @TestTransaction
    void testFindByOrderCodeAndQuestionAndAnswer() {
        // Arrange
        AnswerEntity answer = createTestAnswer(TEST_ORDER_CODE, 10L, 100L);
        answerRepository.persist(answer);

        // Act
        AnswerEntity result = answerRepository.findByOrderCodeAndQuestionAndAnswer(
            TEST_ORDER_CODE, 10L, 100L);

        // Assert
        assertNotNull(result);
        assertEquals(TEST_ORDER_CODE, result.getOrderCode());
        assertEquals(10L, result.getQuestionId());
        assertEquals(100L, result.getAnswerId());

        // Test con valori non esistenti
        AnswerEntity notFound = answerRepository.findByOrderCodeAndQuestionAndAnswer(
            TEST_ORDER_CODE, 999L, 999L);
        assertNull(notFound);
    }

    private AnswerEntity createTestAnswer(String orderCode, Long questionId, Long answerId) {
        AnswerEntity answer = new AnswerEntity();
        answer.setOrderCode(orderCode);
        answer.setProductId(1L);
        answer.setQuestionId(questionId);
        answer.setAnswerId(answerId);
        answer.setCustomerId(1000L);
        answer.setOrderItemId(10000L);
        answer.setCreatedBy("test-user");
        answer.setUpdatedBy("test-user");
        return answer;
    }
}
