-- Script opzionale per aggiungere un constraint unico per prevenire duplicati
-- Questo constraint impedisce di avere la stessa combinazione di order_code, survey_question_id e survey_answers_id

-- Prima rimuovi eventuali duplicati esistenti (mantenendo solo il più recente per ogni combinazione)
DELETE FROM survey.answers a1 
USING survey.answers a2 
WHERE a1.id < a2.id 
  AND a1.order_code = a2.order_code 
  AND a1.survey_question_id = a2.survey_question_id 
  AND a1.survey_answers_id = a2.survey_answers_id;

-- Aggiungi il constraint unico
ALTER TABLE survey.answers 
ADD CONSTRAINT uk_answers_order_question_answer 
UNIQUE (order_code, survey_question_id, survey_answers_id);

-- Commenta la riga sopra se non vuoi il constraint a livello DB
-- Il constraint è opzionale perché la logica applicativa ora gestisce già i duplicati
