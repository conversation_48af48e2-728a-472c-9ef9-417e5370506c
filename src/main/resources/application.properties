tenant=cnpsi-polonia
quarkus.profile=dev
base-url = https://${tenant}-api.${quarkus.profile}.yoloassicurazioni.it
quarkus.banner.enabled= false
quarkus.http.port= 8080
# Configure default tenant datasource
quarkus.datasource.db-kind= postgresql
quarkus.datasource.jdbc.driver= org.postgresql.Driver
# Hibernate
quarkus.hibernate-orm.dialect= org.hibernate.dialect.PostgreSQLDialect
quarkus.hibernate-orm.jdbc.timezone= UTC
quarkus.hibernate-orm.log.sql= true
#quarkus.hibernate-orm.multitenant= DATABASE
# Image build
#quarkus.native.container-build= true
#JWT configuration
# JWT disable token
quarkus.http.auth.proactive= false
quarkus.oauth2.enabled= false
quarkus.security.enabled= false
quarkus.smallrye-jwt.enabled= false
# JWT configuration
# JWT disable token
mp.jwt.verify.publickey.location= publicKey.pem
mp.jwt.decrypt.key.location= keyEncryptPrivate.pem
smallrye.jwt.decrypt.algorithm= RSA_OAEP_256
smallrye.jwt.key-encryption-algorithm= RSA-OAEP-256
# Logging
quarkus.log.console.format= '%d{yyyy-MM-dd HH=mm=ss,SSS} %-5p [%c{2.}] (%t) %s%e%n'
# Open API Settings
quarkus.smallrye-openapi.path= /openapi
quarkus.smallrye-openapi.info-title= Survey API
quarkus.smallrye-openapi.info-version= 1.0.0
# Swagger UI
quarkus.swagger-ui.always-include= true
quarkus.swagger-ui.path= /openapi/swagger-ui
quarkus.rest-client.extensions-api.hostname-verifier= io.quarkus.restclient.NoopHostnameVerifier
quarkus.tls.trust-all= true
quarkus.log.category."org.jboss.resteasy.reactive.client.logging".level= DEBUG
quarkus.log.level= DEBUG
#PRODUCT REST CLIENT
quarkus.rest-client.iad-product.url= ${base-url}/iad-product
quarkus.rest-client.iad-product.scope= javax.inject.Singleton
#ORDER REST CLIENT
quarkus.rest-client.iad-order.url= ${base-url}/iad-order
quarkus.rest-client.iad-order.scope= javax.inject.Singleton
#CUSTOMER REST CLIENT
quarkus.rest-client.iad-customer.url= ${base-url}/iad-customer
quarkus.rest-client.iad-customer.scope= javax.inject.Singleton
#DOCUMENT REST CLIENT
quarkus.rest-client.iad-document.url= ${base-url}/iad-document
quarkus.rest-client.iad-document.scope= javax.inject.Singleton
#PROVIDER-GATEWAY REST CLIENT
quarkus.rest-client.provider-gateway.url= http://providers-gateway-deploy.yep-dev.svc.cluster.local:8080
quarkus.rest-client.provider-gateway.scope= javax.inject.Singleton
quarkus.rest-client.provider-gateway.read-timeout= 60000
quarkus.rest-client.provider-gateway.connect-timeout= 60000


quarkus.datasource.jdbc.url= ************************************************************************************************
quarkus.datasource.password= 1IZld4zia3VIgTNyk6uvRow2G955ZRfYCS
quarkus.datasource.username= cnpsi_polonia_dev
quarkus.oidc.auth-server-url= https://cognito-idp.eu-west-3.amazonaws.com/eu-west-3_hWop30twv
