package it.yolo.repository;

import io.quarkus.hibernate.orm.panache.PanacheRepository;
import it.yolo.entity.AnswerEntity;
import javax.enterprise.context.ApplicationScoped;
import java.util.List;

@ApplicationScoped
public class AnswerRepository implements PanacheRepository<AnswerEntity> {

    /**
     * Trova tutte le risposte per un orderCode specifico
     */
    public List<AnswerEntity> findByOrderCode(String orderCode) {
        return find("orderCode", orderCode).list();
    }

    /**
     * Verifica se esistono già risposte per un orderCode specifico
     */
    public boolean existsByOrderCode(String orderCode) {
        return count("orderCode", orderCode) > 0;
    }

    /**
     * Elimina tutte le risposte per un orderCode specifico
     */
    public long deleteByOrderCode(String orderCode) {
        return delete("orderCode", orderCode);
    }

    /**
     * Trova una risposta specifica per orderCode, questionId e answerId
     */
    public AnswerEntity findByOrderCodeAndQuestionAndAnswer(String orderCode, Long questionId, Long answerId) {
        return find("orderCode = ?1 and questionId = ?2 and answerId = ?3", orderCode, questionId, answerId).firstResult();
    }
}
