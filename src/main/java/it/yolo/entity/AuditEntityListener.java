package it.yolo.entity;

import io.vertx.ext.web.RoutingContext;
import javax.enterprise.inject.spi.CDI;
import javax.persistence.PrePersist;
import javax.persistence.PreUpdate;
import org.jboss.logging.Logger;

public class AuditEntityListener {

    private static final String USER = "unknown";
    private static final Logger LOGGER = Logger.getLogger(
            AuditEntityListener.class);

    @PrePersist
    public void prePersist(Object entity) {
        if (entity instanceof AuditSupport) {
            String tenant = getTenant();
            AuditSupport support = (AuditSupport) entity;
            support.setCreatedBy(tenant);
            support.setUpdatedBy(tenant);
        }
    }

    @PreUpdate
    public void preUpdate(Object entity) {
        if (entity instanceof AuditSupport) {
            AuditSupport support = (AuditSupport) entity;
            support.setUpdatedBy(getTenant());
        }
    }

    private String getTenant() {
        RoutingContext context;

        try {
            context = CDI.current()
                    .select(RoutingContext.class)
                    .get();
        } catch (RuntimeException ex) {
            LOGGER.error("Failed to retrieve RoutingContext bean.", ex);
            return AuditEntityListener.USER;
        }

        return context.get(YoloTenantResolver.PROP_TENANT_ID,
                AuditEntityListener.USER);
    }
}
