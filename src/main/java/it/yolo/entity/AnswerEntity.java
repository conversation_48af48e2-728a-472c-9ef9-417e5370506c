package it.yolo.entity;

import java.time.LocalDateTime;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Entity
@ToString
@Table(name = "answers", schema = "survey")
public class AnswerEntity implements AuditSupport {

    @Id
    @Getter
    @Setter
    @Column(columnDefinition = "serial")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Getter
    @Setter
    @Column(name = "product_id", nullable = false)
    private Long productId;

    @Getter
    @Setter
    @Column(name = "survey_question_id", nullable = false)
    private Long questionId;

    @Getter
    @Setter
    @Column(name = "survey_answers_id", nullable = false)
    private Long answerId;

    @Getter
    @Setter
    @Column(name = "customer_id", nullable = false)
    private Long customerId;

    @Getter
    @Setter
    @Column(name = "order_code", nullable = false)
    private String orderCode;

    @Getter
    @Setter
    @Column(name = "order_item_id", nullable = false)
    private Long orderItemId;

    @Getter
    @Setter
    @Column(name = "created_by", nullable = false, updatable = false)
    private String createdBy;

    @Getter
    @Setter
    @Column(name = "updated_by", nullable = false)
    private String updatedBy;

    @Getter
    @Setter
    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @Getter
    @Setter
    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;
}
