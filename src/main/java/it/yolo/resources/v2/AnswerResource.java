package it.yolo.resources.v2;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.collect.Lists;
import com.google.common.primitives.Longs;
import io.quarkus.security.Authenticated;
import it.yolo.client.OrderClient;
import it.yolo.client.dto.OrderResponse;
import it.yolo.client.dto.SurveyAnswerDto;
import it.yolo.entity.AnswerEntity;
import it.yolo.entity.QuestionEntity;
import it.yolo.exception.EntityNotFoundException;
import it.yolo.exception.ValidationException;
import it.yolo.http.HttpStatus;
import it.yolo.http.ResponseCode;
import it.yolo.mapper.AnswerMapper;
import it.yolo.model.*;
import it.yolo.service.v2.AnswerService;
import it.yolo.service.client.v2.InvokeProductManager;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.media.Content;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import org.eclipse.microprofile.openapi.annotations.parameters.Parameter;
import org.eclipse.microprofile.openapi.annotations.responses.APIResponse;
import org.eclipse.microprofile.rest.client.inject.RestClient;
import org.jboss.resteasy.reactive.ResponseStatus;
import org.jboss.resteasy.reactive.RestPath;

import javax.inject.Inject;
import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.LongStream;

@Path("/v2/answers")
public class AnswerResource {


	@Inject
	InvokeProductManager productManagerClient;
	
	@Inject
	AnswerService service;

	@Inject
	@RestClient
	OrderClient orderClient;
	
	/**
	 * valida la lista delle risposte
	 * se ALMENO una e' false il metodo restituisce false in quanto la richiesta non e' valida
	 * (tutte le risposte devono essere true)
	 *
	 * @param entities
	 * @param productId
	 * @return
	 */
	private boolean validateAnswers(List<AnswerEntity> entities, String token, long productId) {
		String orderCode = entities.stream().findFirst().get().getOrderCode();
		if(!token.contains("Bearer")) token = "Bearer " + token;
		JsonNode orderJson = orderClient.getOrder(token, orderCode).readEntity(JsonNode.class);
		if (productManagerClient.countProductQuestionsById(orderJson) != entities.size()){
			return false;
		}
		for(AnswerEntity a : entities) {
//			if(getAnswerValueFromIadResource(a.getAnswerId(), token).equalsIgnoreCase("NO")) return false;
			/*
			 * 2 cose:
			 * 1 notazione alla yoda - non va in npe se il getAnswer etc restituisce null (anche se non lo fa)
			 * 2 not si fa andare in fault anche se la risposta e' stringa vuota o un testo qualsiasi
			 * (valori sporchi su db)
			 */
			if("prevent_checkout".equalsIgnoreCase(getRuleFromIadResource(a.getAnswerId(), orderJson, token))) return false;
		}

		return true;
	}

	private String getRuleFromIadResource(Long answerId, JsonNode orderResponse, String token) {
		SurveyAnswerDto answer = productManagerClient.readById(answerId, orderResponse, token);
		if(answer != null && answer.getRule() != null) {
			return answer.getRule();
		}

		return "";
	}

	private boolean validateAnswersPacket(List<AnswerEntity> entities, String token,long productId,long packetId) {
		for(AnswerEntity e : entities) {
			QuestionEntity questionEntity = service.readQuestion(e.getQuestionId(), token).orElseThrow(() ->
					new EntityNotFoundException(String.format(
							"Entity question by id=%d not found", e.getQuestionId())));
			if (questionEntity.getPacketId() != null) {
				if (questionEntity.getPacketId() != packetId ) {
					return false;
				}
			}
		}
		return true;
	}

	@POST
	@Authenticated
	@ResponseStatus(HttpStatus.CREATED)
	@Operation(summary = "Creates a new answer returning the created entity")
	@APIResponse(responseCode = ResponseCode.CREATED,
	description = "Entity created",
	content = @Content(mediaType = MediaType.APPLICATION_JSON,
	schema = @Schema(implementation = ResponseSchema.class)))
	@APIResponse(description = "Error",
	content = @Content(mediaType = MediaType.APPLICATION_JSON,
	schema = @Schema(implementation = ErrorResponse.class)))
	public BaseResponseBoundary<List<AnswerBoundaryResponse>> create(
			BaseRequestBoundary<List<AnswerBoundaryRequest>> req,
			@HeaderParam("Authorization") String token
			) throws ValidationException {
		List<AnswerEntity> entities;
		List<AnswerBoundaryResponse> resp;

		entities = AnswerMapper.INSTANCE.requestToEntities(req.getData());

		service.answerChecks(entities, token);
		
		entities = service.createAnswers(entities, token);
		resp = AnswerMapper.INSTANCE.entitiesToResponse(entities);
		return new BaseResponseBoundary<>(resp);
	}

	@GET
	@Authenticated
	@Path("{id}")
	@Operation(summary = "Reads a answer entity")
	@APIResponse(responseCode = ResponseCode.OK, description = "Entity read")
	@APIResponse(description = "Error",
	content = @Content(mediaType = MediaType.APPLICATION_JSON,
	schema = @Schema(implementation = ErrorResponse.class)))
	public BaseResponseBoundary<AnswerBoundaryResponse> read(
			@Parameter(description = "Answer ID")
			@RestPath("id") long id,
			@HeaderParam("Authorization") String token
			) {
		AnswerEntity entity;
		AnswerBoundaryResponse resp;

		entity = service.readAnswer(id, token).orElseThrow(() ->
		new EntityNotFoundException(String.format(
				"Entity answer by id=%d not found", id)));
		resp = AnswerMapper.INSTANCE.entityToResponse(entity);
		return new BaseResponseBoundary<>(resp);
	}

	@PUT
	@Authenticated
	@Path("{id}")
	@Operation(summary = "Updates a answer returning the resulting entity")
	@APIResponse(responseCode = ResponseCode.OK, description = "Entity updated")
	@APIResponse(description = "Error",
	content = @Content(mediaType = MediaType.APPLICATION_JSON,
	schema = @Schema(implementation = ErrorResponse.class)))
	public BaseResponseBoundary<AnswerBoundaryResponse> update(
			@Parameter(description = "Answer ID")
			@RestPath("id") long id,
			BaseRequestBoundary<AnswerBoundaryRequest> req,
			@HeaderParam("Authorization") String token
			) throws ValidationException {
		AnswerEntity entity;
		AnswerBoundaryResponse resp;

		entity = AnswerMapper.INSTANCE.requestToEntity(req.getData());
		
		if(!validateAnswers(Arrays.asList(entity), token, 1)) {
			throw new ValidationException("All answer provided must be \"yes\"");
		}
		
		entity = service.updateAnswer(id, entity, token);
		resp = AnswerMapper.INSTANCE.entityToResponse(entity);
		return new BaseResponseBoundary<>(resp);
	}

	@DELETE
	@Authenticated
	@Path("{id}")
	@ResponseStatus(HttpStatus.NO_CONTENT)
	@Operation(summary = "Deletes a answer")
	@APIResponse(responseCode = ResponseCode.NO_CONTENT, description = "Entity deleted")
	@APIResponse(description = "Error",
	content = @Content(mediaType = MediaType.APPLICATION_JSON,
	schema = @Schema(implementation = ErrorResponse.class)))
	public void delete(
			@Parameter(description = "Answer ID")
			@RestPath("id") long id,
			@HeaderParam("Authorization") String token
			) {
		service.deleteAnswer(id, token);
	}

	@GET
	@Authenticated
	@Operation(summary = "Lists answer entities")
	@APIResponse(responseCode = ResponseCode.OK, description = "Entities read")
	@APIResponse(description = "Error",
	content = @Content(mediaType = MediaType.APPLICATION_JSON,
	schema = @Schema(implementation = ErrorResponse.class)))
	public BaseResponseBoundary<List<AnswerBoundaryResponse>> list(String token) {
		List<AnswerEntity> list;
		List<AnswerBoundaryResponse> resp;

		list = service.listAnswers(token);
		resp = AnswerMapper.INSTANCE.entitiesToResponses(list);
		return new BaseResponseBoundary<>(resp);
	}

	static class ResponseSchema extends
	BaseResponseBoundary<AnswerBoundaryResponse> {

		public ResponseSchema(AnswerBoundaryResponse data) {
			super(data);
		}
	}

	@POST
	@Path("external")
	@Authenticated
	@ResponseStatus(HttpStatus.CREATED)
	@Operation(summary = "Creates a new answer returning the created entity")
	@APIResponse(responseCode = ResponseCode.OK, description = "Entities read")
	@APIResponse(description = "Error",
			content = @Content(mediaType = MediaType.APPLICATION_JSON,
					schema = @Schema(implementation = ErrorResponse.class)))
	public BaseResponseBoundary<List<AnswerBoundaryResponse>>  externalSurvey(
			BaseRequestBoundary<List<AnswerBoundaryRequest>> req,
			@HeaderParam("Authorization") String token
	) throws Exception {
		List<AnswerEntity> entities;
		List<AnswerBoundaryResponse> resp;

		entities = AnswerMapper.INSTANCE.requestToEntities(req.getData());

		service.answerChecks(entities, token);
		service.externalSurvey(entities, token);
		resp = AnswerMapper.INSTANCE.entitiesToResponse(entities);
		return new BaseResponseBoundary<>(resp);
	}
}
