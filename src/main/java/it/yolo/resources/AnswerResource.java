package it.yolo.resources;

import io.quarkus.security.Authenticated;
import it.yolo.client.dto.SurveyAnswerDto;
import it.yolo.entity.AnswerEntity;
import it.yolo.exception.EntityNotFoundException;
import it.yolo.exception.ValidationException;
import it.yolo.http.HttpStatus;
import it.yolo.http.ResponseCode;
import it.yolo.mapper.AnswerMapper;
import it.yolo.model.BaseRequestBoundary;
import it.yolo.model.BaseResponseBoundary;
import it.yolo.model.AnswerBoundaryRequest;
import it.yolo.model.AnswerBoundaryResponse;
import it.yolo.model.ErrorResponse;
import it.yolo.service.AnswerService;
import it.yolo.service.client.InvokeProductManager;

import java.util.Arrays;
import java.util.List;
import javax.inject.Inject;
import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.media.Content;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import org.eclipse.microprofile.openapi.annotations.parameters.Parameter;
import org.eclipse.microprofile.openapi.annotations.responses.APIResponse;
import org.jboss.resteasy.reactive.ResponseStatus;
import org.jboss.resteasy.reactive.RestPath;

@Path("/v1/answers")
public class AnswerResource {


	@Inject
	InvokeProductManager productManagerClient;
	
	@Inject
	AnswerService service;
	
	/**
	 * valida la lista delle risposte
	 * se ALMENO una e' false il metodo restituisce false in quanto la richiesta non e' valida
	 * (tutte le risposte devono essere true)
	 * @param entities
	 * @return
	 */
	private boolean validateAnswers(List<AnswerEntity> entities, String token) {
		
		for(AnswerEntity a : entities) {
//			if(getAnswerValueFromIadResource(a.getAnswerId(), token).equalsIgnoreCase("NO")) return false;
			/*
			 * 2 cose:
			 * 1 notazione alla yoda - non va in npe se il getAnswer etc restituisce null (anche se non lo fa)
			 * 2 not si fa andare in fault anche se la risposta e' stringa vuota o un testo qualsiasi
			 * (valori sporchi su db)
			 */
			if(!"SI".equalsIgnoreCase(getAnswerValueFromIadResource(a.getAnswerId(), token))) return false;
		}
		
		return true;
	}
	

	/**
	 * il metodo invoca via rest iad-product per ottenere dalla tabella
	 * survey answer se su db la risposta e' si o no
	 * @param answerId
	 * @return
	 */
	private String getAnswerValueFromIadResource(Long answerId, String token) {
		SurveyAnswerDto answer = productManagerClient.readById(answerId, token);
		if(answer != null && answer.getValue() != null) {
			return answer.getValue();
		}
		
		return "";
	}

	@POST
	@Authenticated
	@ResponseStatus(HttpStatus.CREATED)
	@Operation(summary = "Creates a new answer returning the created entity")
	@APIResponse(responseCode = ResponseCode.CREATED,
	description = "Entity created",
	content = @Content(mediaType = MediaType.APPLICATION_JSON,
	schema = @Schema(implementation = ResponseSchema.class)))
	@APIResponse(description = "Error",
	content = @Content(mediaType = MediaType.APPLICATION_JSON,
	schema = @Schema(implementation = ErrorResponse.class)))
	public BaseResponseBoundary<List<AnswerBoundaryResponse>> create(
			BaseRequestBoundary<List<AnswerBoundaryRequest>> req,
			@HeaderParam("Authorization") String token
			) throws ValidationException {
		List<AnswerEntity> entities;
		List<AnswerBoundaryResponse> resp;

		entities = AnswerMapper.INSTANCE.requestToEntities(req.getData());
		
		if(!validateAnswers(entities, token)) {
			throw new ValidationException("All answer provided must be \"yes\"");
		}
		
		entities = service.createAnswers(entities, token);
		resp = AnswerMapper.INSTANCE.entitiesToResponse(entities);
		return new BaseResponseBoundary<>(resp);
	}

	@GET
	@Authenticated
	@Path("{id}")
	@Operation(summary = "Reads a answer entity")
	@APIResponse(responseCode = ResponseCode.OK, description = "Entity read")
	@APIResponse(description = "Error",
	content = @Content(mediaType = MediaType.APPLICATION_JSON,
	schema = @Schema(implementation = ErrorResponse.class)))
	public BaseResponseBoundary<AnswerBoundaryResponse> read(
			@Parameter(description = "Answer ID")
			@RestPath("id") long id,
			@HeaderParam("Authorization") String token
			) {
		AnswerEntity entity;
		AnswerBoundaryResponse resp;

		entity = service.readAnswer(id, token).orElseThrow(() ->
		new EntityNotFoundException(String.format(
				"Entity answer by id=%d not found", id)));
		resp = AnswerMapper.INSTANCE.entityToResponse(entity);
		return new BaseResponseBoundary<>(resp);
	}

	@PUT
	@Authenticated
	@Path("{id}")
	@Operation(summary = "Updates a answer returning the resulting entity")
	@APIResponse(responseCode = ResponseCode.OK, description = "Entity updated")
	@APIResponse(description = "Error",
	content = @Content(mediaType = MediaType.APPLICATION_JSON,
	schema = @Schema(implementation = ErrorResponse.class)))
	public BaseResponseBoundary<AnswerBoundaryResponse> update(
			@Parameter(description = "Answer ID")
			@RestPath("id") long id,
			BaseRequestBoundary<AnswerBoundaryRequest> req,
			@HeaderParam("Authorization") String token
			) throws ValidationException {
		AnswerEntity entity;
		AnswerBoundaryResponse resp;

		entity = AnswerMapper.INSTANCE.requestToEntity(req.getData());
		
		if(!validateAnswers(Arrays.asList(entity), token)) {
			throw new ValidationException("All answer provided must be \"yes\"");
		}
		
		entity = service.updateAnswer(id, entity, token);
		resp = AnswerMapper.INSTANCE.entityToResponse(entity);
		return new BaseResponseBoundary<>(resp);
	}

	@DELETE
	@Authenticated
	@Path("{id}")
	@ResponseStatus(HttpStatus.NO_CONTENT)
	@Operation(summary = "Deletes a answer")
	@APIResponse(responseCode = ResponseCode.NO_CONTENT, description = "Entity deleted")
	@APIResponse(description = "Error",
	content = @Content(mediaType = MediaType.APPLICATION_JSON,
	schema = @Schema(implementation = ErrorResponse.class)))
	public void delete(
			@Parameter(description = "Answer ID")
			@RestPath("id") long id,
			@HeaderParam("Authorization") String token
			) {
		service.deleteAnswer(id, token);
	}

	@GET
	@Authenticated
	@Operation(summary = "Lists answer entities")
	@APIResponse(responseCode = ResponseCode.OK, description = "Entities read")
	@APIResponse(description = "Error",
	content = @Content(mediaType = MediaType.APPLICATION_JSON,
	schema = @Schema(implementation = ErrorResponse.class)))
	public BaseResponseBoundary<List<AnswerBoundaryResponse>> list(String token) {
		List<AnswerEntity> list;
		List<AnswerBoundaryResponse> resp;

		list = service.listAnswers(token);
		resp = AnswerMapper.INSTANCE.entitiesToResponses(list);
		return new BaseResponseBoundary<>(resp);
	}

	@GET
	@Authenticated
	@Path("order")
	@Operation(summary = "Lists order answer entities")
	@APIResponse(responseCode = ResponseCode.OK, description = "Entities read")
	@APIResponse(description = "Error",
			content = @Content(mediaType = MediaType.APPLICATION_JSON,
					schema = @Schema(implementation = ErrorResponse.class)))
	public BaseResponseBoundary<List<AnswerBoundaryResponse>> listOrderAnswers(String token, @QueryParam("order") String orderCode) {
		List<AnswerEntity> list;
		List<AnswerBoundaryResponse> resp;

		list = service.listOrderAnswers(orderCode);
		resp = AnswerMapper.INSTANCE.entitiesToResponses(list);
		return new BaseResponseBoundary<>(resp);
	}

	static class ResponseSchema extends
	BaseResponseBoundary<AnswerBoundaryResponse> {

		public ResponseSchema(AnswerBoundaryResponse data) {
			super(data);
		}
	}
}
