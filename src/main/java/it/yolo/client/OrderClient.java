package it.yolo.client;

import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;
import org.jboss.resteasy.reactive.RestHeader;

import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.core.Response;

@Path("/v3")
@RegisterRestClient(configKey = "iad-order")
public interface OrderClient {

    @GET
    @Path("/code/{orderCode}")
    Response getOrder(@RestHeader("Authorization") String token, String orderCode);
}
