package it.yolo.client;

import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.core.Response;

import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;
//import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;
import org.jboss.resteasy.reactive.RestHeader;
@Path("/v1/answers")
@RegisterRestClient(configKey = "iad-product")
public interface ProductManagerClient {
	
	@GET
    @Path("{id}")
    Response findById(@RestHeader("Authorization") String token, Long id);

}
