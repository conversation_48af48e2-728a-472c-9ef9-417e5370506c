package it.yolo.client.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;

import java.util.List;

public class QuestionDto {
    @JsonProperty("id")
    private long id;

    @JsonProperty("answers")
    private List<AnswerDto> answers;

    private String content;

    private int position;

    private int packet_id;

    private JsonNode rule;

    public long getId() {return id;}

    public List<AnswerDto> getAnswers() {return answers;}

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public int getPosition() {
        return position;
    }

    public void setPosition(int position) {
        this.position = position;
    }

    public int getPacket_id() {
        return packet_id;
    }

    public void setPacket_id(int packet_id) {
        this.packet_id = packet_id;
    }

    public JsonNode getRule() {
        return rule;
    }

    public void setRule(JsonNode rule) {
        this.rule = rule;
    }
}
