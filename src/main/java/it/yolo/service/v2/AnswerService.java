package it.yolo.service.v2;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.collect.Lists;
import com.google.common.primitives.Longs;
import io.opentelemetry.instrumentation.annotations.SpanAttribute;
import io.opentelemetry.instrumentation.annotations.WithSpan;
import it.yolo.client.CustomerClient;
import it.yolo.client.DocumentClient;
import it.yolo.client.OrderClient;
import it.yolo.client.ProductClient;
import it.yolo.client.dto.PGClient;
import it.yolo.client.dto.QuestionDto;
import it.yolo.client.dto.SurveyAnswerDto;
import it.yolo.entity.AnswerEntity;
import it.yolo.entity.QuestionEntity;
import it.yolo.exception.EntityNotFoundException;
import it.yolo.model.AnswerBoundaryRequest;
import it.yolo.model.BaseRequestBoundary;
import it.yolo.repository.AnswerRepository;
import it.yolo.repository.QuestionRepository;
import it.yolo.service.client.v2.InvokeProductManager;
import org.eclipse.microprofile.rest.client.inject.RestClient;

import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import javax.transaction.Transactional;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;

@RequestScoped
public class AnswerService {
    @Inject
    AnswerRepository repo;

    @Inject
    QuestionRepository questionRepository;

    @Inject
    InvokeProductManager productManagerClient;

    @Inject
    @RestClient
    DocumentClient documentClient;

    @Inject
    @RestClient
    CustomerClient customerClient;

    @Inject
    @RestClient
    PGClient pgClient;

    @Inject
    @RestClient
    OrderClient orderClient;

    @Inject
    @RestClient
    ProductClient productClient;

    @Transactional
    @WithSpan("AnswerService.createAnswer")
    public List<AnswerEntity> createAnswers(
            @SpanAttribute("arg.entity") List<AnswerEntity> entities,
            String token
    ) {
        return createOrUpdateAnswers(entities, token);
    }

    /**
     * Crea o aggiorna le risposte per un orderCode.
     * Se esistono già risposte per l'orderCode, le sostituisce con quelle nuove.
     * Questo previene la creazione di duplicati.
     */
    @Transactional
    @WithSpan("AnswerService.createOrUpdateAnswers")
    public List<AnswerEntity> createOrUpdateAnswers(
            @SpanAttribute("arg.entity") List<AnswerEntity> entities,
            String token
    ) {
        if (entities == null || entities.isEmpty()) {
            return entities;
        }

        // Ottieni l'orderCode dal primo elemento (assumendo che tutte le risposte abbiano lo stesso orderCode)
        String orderCode = entities.get(0).getOrderCode();

        // Verifica se esistono già risposte per questo orderCode
        if (repo.existsByOrderCode(orderCode)) {
            // Elimina le risposte esistenti per questo orderCode
            repo.deleteByOrderCode(orderCode);
            // Flush per assicurarsi che la cancellazione sia completata prima dell'inserimento
            repo.flush();
        }

        // Persisti le nuove risposte
        repo.persist(entities);
        return entities;
    }

    @WithSpan("AnswerService.readAnswer")
    public Optional<AnswerEntity> readAnswer(
            @SpanAttribute("arg.id") long id,
            String token
    ) {
        return repo.findByIdOptional(id);
    }

    /**
     * Recupera tutte le risposte esistenti per un orderCode specifico
     */
    @WithSpan("AnswerService.getAnswersByOrderCode")
    public List<AnswerEntity> getAnswersByOrderCode(
            @SpanAttribute("arg.orderCode") String orderCode
    ) {
        return repo.findByOrderCode(orderCode);
    }

    /**
     * Verifica se esistono già risposte per un orderCode specifico
     */
    @WithSpan("AnswerService.existsAnswersByOrderCode")
    public boolean existsAnswersByOrderCode(
            @SpanAttribute("arg.orderCode") String orderCode
    ) {
        return repo.existsByOrderCode(orderCode);
    }

    @WithSpan("AnswerService.readQuestion")
    public Optional<QuestionEntity> readQuestion(
            @SpanAttribute("arg.id") long id,
            String token
    ) {
        return questionRepository.findByIdOptional(id);
    }

    @Transactional
    @WithSpan("AnswerService.updateAnswer")
    public AnswerEntity updateAnswer(
            @SpanAttribute("arg.id") long id,
            @SpanAttribute("arg.entity") AnswerEntity entity,
            String token
    ) {
        AnswerEntity updating = repo.findByIdOptional(id).orElseThrow(() ->
                new EntityNotFoundException(String.format(
                        "Entity answer by id=%d not found", id)));

        // TODO
        repo.getEntityManager().merge(updating);
        return updating;
    }

    @Transactional
    @WithSpan("AnswerService.deleteAnswer")
    public void deleteAnswer(@SpanAttribute("arg.id") long id, String token) {
        repo.deleteById(id);
    }

    @WithSpan("AnswerService.listAnswers")
    public List<AnswerEntity> listAnswers(String token) {
        return repo.listAll();
    }

    @WithSpan("AnswerService.listAnswers")
    public List<AnswerEntity> listOrderAnswers(String order) {
        return repo.find("order_code", order).list();
    }

    public boolean answerChecks(List<AnswerEntity> productSurvey, String token) {
        ObjectMapper mapper = new ObjectMapper();
        String orderCode = productSurvey.stream().findFirst().get().getOrderCode();
        if (!token.contains("Bearer")) token = "Bearer " + token;
        JsonNode orderJson = orderClient.getOrder(token, orderCode).readEntity(JsonNode.class);
        QuestionDto[] questions;
        try {
            questions = mapper.treeToValue(orderJson.get("data").get("product").get("data").withArray("questions"), QuestionDto[].class);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        validateAnswers(productSurvey, orderJson, token);
        validateAnswersQuestionsValues(productSurvey, questions);
        validateAnswersCount(productSurvey, orderJson);
        return true;
    }

    public void externalSurvey(List<AnswerEntity> req, String token) throws Exception {
        // first method to generate the survey pdf
        ObjectNode reqIadDocument = JsonNodeFactory.instance.objectNode();
        String orderCode = req.get(0).getOrderCode();
        JsonNode orderResponse = orderClient.getOrder(token, orderCode).readEntity(JsonNode.class);

        ObjectNode productData = (ObjectNode) orderResponse.get("data").get("product");

        // call orderClient to retrieve order data

        Long customerId = orderResponse.get("data").get("customerId").asLong();

        // call customerClient to retrieve customer data
        JsonNode customerResponse = customerClient.findById(token, customerId).readEntity(JsonNode.class);

        // create the classic request for iad-document to generate the external pdf
        reqIadDocument.put("customer", customerResponse.get("data"));
        reqIadDocument.put("order", orderResponse.get("data"));
        reqIadDocument.put("product", productData.get("data"));
        reqIadDocument.put("policy", JsonNodeFactory.instance.objectNode());

        // call iad-document to generate pdf file
        ObjectNode documentData = documentClient.generateDocument(reqIadDocument).readEntity(ObjectNode.class);
        ObjectNode filteredProduct = (ObjectNode) productData.get("data");

        // remove to resolve request body is too large exception
        filteredProduct.remove("configuration");
        filteredProduct.remove("packets");

        productData.set("data", filteredProduct);
        documentData.put("product", productData);
        documentData.put("order", orderResponse);

        // call the pgw to send the pdf to the company
        ObjectNode externalRequestData = JsonNodeFactory.instance.objectNode();
        ObjectNode externalRequestBody = JsonNodeFactory.instance.objectNode();
        externalRequestData.put("data", documentData);
        pgClient.externalSurvey(externalRequestData).readEntity(JsonNode.class);
            }

    private boolean validateAnswers(List<AnswerEntity> entities, JsonNode orderJson, String token) {
        if (productManagerClient.countProductQuestionsById(orderJson) != entities.size()) {
            return false;
        }
        for (AnswerEntity a : entities) {
//			if(getAnswerValueFromIadResource(a.getAnswerId(), token).equalsIgnoreCase("NO")) return false;
            /*
             * 2 cose:
             * 1 notazione alla yoda - non va in npe se il getAnswer etc restituisce null (anche se non lo fa)
             * 2 not si fa andare in fault anche se la risposta e' stringa vuota o un testo qualsiasi
             * (valori sporchi su db)
             */
            if ("prevent_checkout".equalsIgnoreCase(getRuleFromIadResource(a.getAnswerId(), orderJson, token)))
                return false;
        }

        return true;
    }

    private boolean validateAnswersQuestionsValues(List<AnswerEntity> entities, QuestionDto[] questions){
        HashMap<Long, List<Long>> map = productManagerClient.productQuestionAnswerMap(questions);
        for(AnswerEntity a : entities) {

            if(!map.get(a.getQuestionId()).contains(a.getAnswerId())){
                return true;
            }
        }

        return false;
    }

    private boolean validateAnswersCount(List<AnswerEntity> entities, JsonNode orderJson) {
        return productManagerClient.countProductQuestionsById(orderJson) == entities.size();
    }

    private boolean validateAnswersProductRelationship(List<AnswerEntity> entities, QuestionDto[] questions){ //passare product questions
        List<Long> allowedValues = Lists.newArrayList(Longs.asList(productManagerClient.getProductQuestionsIDs(questions)));
        List<Long> userValues = Lists.newArrayList(Longs.asList(entities.stream().mapToLong(AnswerEntity::getQuestionId).toArray()));
        userValues.removeAll(allowedValues);
        return userValues.isEmpty();
    }

    private boolean validateAnswersPacket(List<AnswerEntity> entities, String token,long productId,long packetId) {
        for(AnswerEntity e : entities) {
            QuestionEntity questionEntity = readQuestion(e.getQuestionId(), token).orElseThrow(() ->
                    new EntityNotFoundException(String.format(
                            "Entity question by id=%d not found", e.getQuestionId())));
            if (questionEntity.getPacketId() != null) {
                if (questionEntity.getPacketId() != packetId ) {
                    return false;
                }
            }
        }
        return true;
    }

    private String getRuleFromIadResource(Long answerId, JsonNode orderResponse, String token) {
        SurveyAnswerDto answer = productManagerClient.readById(answerId, orderResponse, token);
        if (answer != null && answer.getRule() != null) {
            return answer.getRule();
        }
        return "";
    }
}
