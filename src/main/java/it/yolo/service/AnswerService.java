package it.yolo.service;

import io.opentelemetry.instrumentation.annotations.SpanAttribute;
import io.opentelemetry.instrumentation.annotations.WithSpan;
import it.yolo.entity.AnswerEntity;
import it.yolo.entity.QuestionEntity;
import it.yolo.exception.EntityNotFoundException;
import it.yolo.repository.AnswerRepository;
import it.yolo.repository.QuestionRepository;

import java.util.List;
import java.util.Optional;
import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import javax.transaction.Transactional;

@ApplicationScoped
public class AnswerService {

    @Inject
    AnswerRepository repo;

    @Inject
    QuestionRepository questionRepository;

    @Transactional
    @WithSpan("AnswerService.createAnswer")
    public List<AnswerEntity> createAnswers(
            @SpanAttribute("arg.entity") List<AnswerEntity> entities,
            String token
    ) {
        repo.persist(entities);
        return entities;
    }

    @WithSpan("AnswerService.readAnswer")
    public Optional<AnswerEntity> readAnswer(
            @SpanAttribute("arg.id") long id,
            String token
    ) {
        return repo.findByIdOptional(id);
    }

    @Transactional
    @WithSpan("AnswerService.updateAnswer")
    public AnswerEntity updateAnswer(
            @SpanAttribute("arg.id") long id,
            @SpanAttribute("arg.entity") AnswerEntity entity,
            String token
    ) {
        AnswerEntity updating = repo.findByIdOptional(id).orElseThrow(() ->
                new EntityNotFoundException(String.format(
                        "Entity answer by id=%d not found", id)));

        // TODO
        repo.getEntityManager().merge(updating);
        return updating;
    }

    @Transactional
    @WithSpan("AnswerService.deleteAnswer")
    public void deleteAnswer(@SpanAttribute("arg.id") long id, String token) {
        repo.deleteById(id);
    }

    @WithSpan("AnswerService.listAnswers")
    public List<AnswerEntity> listAnswers(String token) {
        return repo.listAll();
    }

    @WithSpan("AnswerService.listAnswers")
    public List<AnswerEntity> listOrderAnswers(String order) {
        return repo.find("order_code", order).list();
    }

    @WithSpan("AnswerService.readQuestion")
    public Optional<QuestionEntity> readQuestion(
            @SpanAttribute("arg.id") long id,
            String token
    ) {
        return questionRepository.findByIdOptional(id);
    }

}
