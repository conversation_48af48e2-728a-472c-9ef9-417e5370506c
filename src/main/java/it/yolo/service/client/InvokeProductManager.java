package it.yolo.service.client;

import it.yolo.client.dto.SurveyAnswerDto;

import java.util.HashMap;
import java.util.List;

public interface InvokeProductManager {
	
	public SurveyAnswerDto readById(long id, String token);
	public int countProductQuestionsById(String orderCode, String token);

	public long[] getProductQuestionsIDs(long id, String token);


	public HashMap<Long, List<Long>> productQuestionAnswerMap(long id, String token);

}
