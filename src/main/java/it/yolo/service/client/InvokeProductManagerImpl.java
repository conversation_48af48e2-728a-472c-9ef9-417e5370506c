package it.yolo.service.client;

import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.primitives.Longs;
import it.yolo.client.OrderClient;
import it.yolo.client.ProductClient;
import it.yolo.client.ProductManagerClient;
import it.yolo.client.dto.ProductResponse;
import it.yolo.client.dto.QuestionDto;
import it.yolo.client.dto.SurveyAnswerDto;
import it.yolo.client.dto.SurveyAnswerResponse;
import org.eclipse.microprofile.rest.client.inject.RestClient;

import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import javax.ws.rs.core.Response;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.stream.LongStream;


@RequestScoped
public class InvokeProductManagerImpl implements InvokeProductManager{
	
	@Inject
    @RestClient
    ProductManagerClient pmClient;

    @Inject
    @RestClient
    ProductClient productClient;

    @Inject
    @RestClient
    OrderClient orderClient;

//    @Inject
//    JsonWebToken jsonWebToken;

    ProductResponse getProductResponse(long id, String token){
        if(!token.contains("Bearer")) token = "Bearer " + token;
        Response res = productClient.findProductById(token, id);
        return res.readEntity(ProductResponse.class);
    }

	@Override
	public SurveyAnswerDto readById(long id, String token) {
//		String jwt = "Bearer " + /*jsonWebToken.getRawToken()*/ token;
		if(!token.contains("Bearer")) token = "Bearer " + token;
        Response res = pmClient.findById(token, id);
        SurveyAnswerResponse policyResponseDto = res.readEntity(SurveyAnswerResponse.class);
        return policyResponseDto != null? policyResponseDto.getData() : null;
	}

    @Override
    public int countProductQuestionsById(String orderCode, String token) {
//		String jwt = "Bearer " + /*jsonWebToken.getRawToken()*/ token;
        JsonNode orderJson = orderClient.getOrder(token, orderCode).readEntity(JsonNode.class);
        return orderJson.get("data").get("packet").get("data").withArray("questions").size();
    }

    public long[] getProductQuestionsIDs(long id, String token) {
        ProductResponse productResponse = getProductResponse(id, token);
        return Arrays.stream(productResponse.getData().getQuestions()).flatMapToLong(question -> LongStream.of(question.getId())).toArray();
    }

    @Override
    public HashMap<Long,List<Long>> productQuestionAnswerMap(long id, String token){
        HashMap<Long, List<Long>> qaMap = new HashMap<>();
        QuestionDto[] questions = getProductResponse(id, token).getData().getQuestions();
        for (QuestionDto question:
             questions) {
                qaMap.put(question.getId(), Longs.asList(question.getAnswers().stream().mapToLong(answer -> answer.getId()).toArray()));
        }
        return qaMap;
    }

}
