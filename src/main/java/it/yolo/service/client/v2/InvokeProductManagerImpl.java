package it.yolo.service.client.v2;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.google.common.primitives.Longs;
import it.yolo.client.OrderClient;
import it.yolo.client.ProductClient;
import it.yolo.client.ProductManagerClient;
import it.yolo.client.dto.*;
import org.eclipse.microprofile.rest.client.inject.RestClient;

import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import javax.ws.rs.core.Response;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.LongStream;
import java.util.stream.StreamSupport;


@RequestScoped
public class InvokeProductManagerImpl implements InvokeProductManager {
	
	@Inject
    @RestClient
    ProductManagerClient pmClient;

    @Inject
    @RestClient
    ProductClient productClient;

    @Inject
    @RestClient
    OrderClient orderClient;

//    @Inject
//    JsonWebToken jsonWebToken;

    ProductResponse getProductResponse(long id, String token){
        if(!token.contains("Bearer")) token = "Bearer " + token;
        Response res = productClient.findProductById(token, id);
        return res.readEntity(ProductResponse.class);
    }

	@Override
	public SurveyAnswerDto readById(Long answerId, JsonNode orderResponse, String token) {
//		String jwt = "Bearer " + /*jsonWebToken.getRawToken()*/ token;
        JsonNode answerJson = StreamSupport.stream(orderResponse.get("data").get("packet").get("data").withArray("questions").spliterator(), false).flatMap(question -> StreamSupport.stream(question.withArray("answers").spliterator(), false)).collect(Collectors.toList()).stream().filter(answer -> answer.get("id").asInt() == answerId).findFirst().get();
        ObjectMapper mapper = new ObjectMapper();
        SurveyAnswerDto answerDto;
        try {
            answerDto = mapper.treeToValue(answerJson, SurveyAnswerDto.class);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        return answerDto;
	}

    @Override
    public int countProductQuestionsById(JsonNode orderResponse) {
        return orderResponse.get("data").get("packet").get("data").withArray("questions").size();
    }

    public long[] getProductQuestionsIDs(QuestionDto[] questions) {
        return Arrays.stream(questions).flatMapToLong(question -> LongStream.of(question.getId())).toArray();
    }

    @Override
    public HashMap<Long,List<Long>> productQuestionAnswerMap(QuestionDto[] questions){ //richiede product.questions
        HashMap<Long, List<Long>> qaMap = new HashMap<>();
        for (QuestionDto question:
             questions) {
                qaMap.put(question.getId(), Longs.asList(question.getAnswers().stream().mapToLong(answer -> answer.getId()).toArray()));
        }
        return qaMap;
    }

}
