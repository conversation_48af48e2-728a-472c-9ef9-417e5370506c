package it.yolo.model;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.eclipse.microprofile.openapi.annotations.media.Schema;

@Schema(description = "Question data")
public class AnswerBoundaryRequest {

    @Valid
    @Getter
    @Setter
    @NotNull
    @Schema(description = "orderCode")
    private String orderCode;

    @Valid
    @Getter
    @Setter
    @NotNull
    @Schema(description = "productId")
    private long productId;

    @Valid
    @Getter
    @Setter
    @NotNull
    @Schema(description = "Order Item")
    private Long orderItemId;

    @Valid
    @Getter
    @Setter
    @NotNull
    @Schema(description = "Question ID")
    private long questionId;

    @Valid
    @Getter
    @Setter
    @NotNull
    @Schema(description = "Answer ID")
    private long answerId;
}
